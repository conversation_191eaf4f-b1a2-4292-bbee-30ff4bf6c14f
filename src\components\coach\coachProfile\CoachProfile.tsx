'use client'
import PreferedLocations from "@/components/common/PreferedLocations"
import ProfilesGallery from "@/components/common/ProfilesGallery"
import QuickLinks from "@/components/common/QuickLinks"
import ScrollToTop from "@/components/common/ScrollToTop"
import SocialMedia from "@/components/common/SocialMedia"
import EditableSocialMedia from "@/components/common/EditableSocialMedia"
import SimpleSocialMediaEdit from "@/components/common/SimpleSocialMediaEdit"
import FlexibleSocialMediaEdit from "@/components/common/FlexibleSocialMediaEdit"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { fetchCoachProfile, getCoachAffiliationType, getCoachBackgroundAll, getCoachFocusAll, getCoachFocusById, getCoachOffer, getCoachOfferTags, getCoachPlatformTags, getCoachSelectedBackgrounds, getCoachTimeZonesType, getCoachWhyonCA, handleCoachInputChange, fetchCoachSocialMediaLinks, postCoachQuickLinks, fetchCoachQuickLinks, fetchCoachGallery, fetchCoachVideoHighlight, fetchCoachAvailability, fetchCoachSports } from "@/store/slices/coach/coachProfileSlice"
import { AddedStateLocationsItem, Option } from "@/utils/interfaces"
import { useDispatch, useSelector } from "react-redux"
import ProfileUrl from "../../common/ProfileUrl"
import CoachLinkTracker from "../../common/SectionsLinkTracker"
import CoachAboutCard from "./CoachAboutCard"
import CoachAffiliation from "./CoachAfifliation"
import CoachBio from "./CoachBio"
import CoachCertifications from "./CoachCertifications"
import CoachContactInfo from "./CoachContactInfo"
import CoachDeclaration from "./CoachDeclaration"
import CoachFocusSection from "./CoachFocusSection"
import CoachQuestionary from "./CoachQuestionary"
import CoachResume from "./CoachResume"
import CoachVerification from "./CoachVerification"
import CoachVideo from "./CoachVideo"
import GeneralAvailability from "./GeneralAvailability"
import LatestHighLightVideo from "./LatestHighLightVideo"
import SportsInfo from "./SportsInfo"
import { useEffect } from "react"
import { fetchAllSpecialities, getCoachDocType } from "@/store/slices/commonSlice"

const linkTrackList = [
    { id: 'myFocus', img: '/my-focus.png', label: 'My Focus', },
    { id: 'aboutMe', img: '/about-me.png', label: 'About Me', },
    { id: 'mySports', img: '/my-sports.webp', label: 'My Sports', },
    { id: 'media', img: '/media.png', label: 'Media', },
    { id: 'accomplisments', img: '/accomplisments.png', label: 'Accomplisments', },
    { id: 'socialMedia', img: '/social-media.png', label: 'Social Media', },
    { id: 'affiliation', img: '/affiliation.png', label: 'Affiliation', },
    { id: 'approach', img: '/approach.png', label: 'Approach', },
    { id: 'location', img: '/location.svg', label: 'Location', },
    { id: 'avaialability', img: '/availability.png', label: 'Avaialability', },
    { id: 'contact', img: '/contact.png', label: 'Contact', },
];

const CoachProfile = () => {
    const { coachSocialMediaList, toggleSocialMedia, coachQuickLinksList,
        toggleQuickLinks, toggleWebsite, website, openVirtualSession, selectedState, selectedCounties,
        selectedLocations, coachAddedStateLocationsList, toggleGallery, galleriesList,coachProfileData } = useSelector((state: RootState) => state.coachProfile)

    const dispatch = useDispatch<AppDispatch>()
    const handleToggleChange = (name: string, checked: boolean) => {
        dispatch(handleCoachInputChange({ name, value: checked }))
    }

    console.log(coachQuickLinksList,"coachQuickLinksList data")


    const handleStateLocations = (name: string, value: Option | Option[]) => {
        if (name === 'selectedState') {
            dispatch(handleCoachInputChange({ name, value }))
            dispatch(handleCoachInputChange({ name: 'selectedLocations', value: [] }))
        } else if (name === 'selectedLocations') {
            dispatch(handleCoachInputChange({ name, value }))
        }
    }

    const handleUpdateAddedStateLocations = (updatedList: AddedStateLocationsItem[]) => {
        dispatch(handleCoachInputChange({ name: 'coachAddedStateLocationsList', value: [...updatedList] }))
    }

    const handleOpenVirtualSession = (checked) => {
        // Open session API call
    }

    useEffect(() => {
        dispatch(fetchCoachProfile())
        dispatch(getCoachFocusById())
        dispatch(getCoachFocusAll())
        dispatch(getCoachBackgroundAll())
        dispatch(getCoachSelectedBackgrounds())
        dispatch(getCoachAffiliationType())
        dispatch(getCoachTimeZonesType())
        dispatch(getCoachPlatformTags())
        dispatch(getCoachOfferTags())
        dispatch(getCoachDocType())
        dispatch(getCoachWhyonCA())
        dispatch(getCoachOffer())
        dispatch(fetchCoachSocialMediaLinks())
        dispatch(fetchCoachQuickLinks())
        dispatch(fetchCoachGallery())
        dispatch(fetchCoachVideoHighlight())
        dispatch(fetchCoachAvailability())  
        dispatch(fetchCoachSports())      
    }, [])

    return (
        <>
            <div className="flex flex-col flex-grow w-full flex-grow w-full">

                <div className="flex flex-col gap-11 py-8">
                    <ProfileUrl />

                    <CoachLinkTracker linkTrackList={linkTrackList} />

                    <CoachAboutCard />

                    <div id="myFocus">
                        <CoachFocusSection />
                    </div>

                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-5">
                        <div className="flex flex-col gap-8" id='aboutMe'>
                            <CoachVideo />
                            <CoachBio />
                        </div>
                        <div className="flex flex-col gap-8" id='socialMedia'>
                            <FlexibleSocialMediaEdit
                                origin="coach"
                                list={coachSocialMediaList}
                                toggleSocialMediaSection={toggleSocialMedia}
                                onChangeToggleSection={(checked) => handleToggleChange('toggleSocialMedia', checked)}
                            />
                            <QuickLinks
                                origin="coach"
                                list={coachQuickLinksList}
                                toggleQuickLinkSection={toggleQuickLinks}
                                onChangeToggleSection={(checked) => handleToggleChange('toggleQuickLinks', checked)}
                            />
                        </div>

                    </div>

                    <div id='affiliation'>
                        <CoachAffiliation />
                    </div>

                    <CoachQuestionary />

                    <div id='mySports'>
                        <SportsInfo />
                        
                    </div>

                    <div className="flex items-center gap-4 bg-slate-200 rounded-lg p-4">
                        <Label htmlFor="openVirtualSession" className="font-bold">Open To Virtual Sessions</Label>
                        <Switch
                            name='openVirtualSession'
                            id={'openVirtualSession'}
                            checked={openVirtualSession}
                            onCheckedChange={handleOpenVirtualSession}
                        />
                    </div>

                    <div id='location'>
                        <PreferedLocations
                            selectedState={selectedState}
                            selectedCounties={selectedCounties}
                            selectedLocations={selectedLocations}
                            handleStateLocations={handleStateLocations}
                            addedStateLocationsList={coachAddedStateLocationsList}
                            handleUpdateAddedStateLocations={handleUpdateAddedStateLocations}
                        />
                    </div>


                    <div className="grid grid-cols-1 xl:grid-cols-2 gap-5" id='media'>
                        <LatestHighLightVideo />
                        <ProfilesGallery
                            toggleGallery={toggleGallery}
                            handleToggleSection={handleToggleChange}
                            list={galleriesList}
                            origin="Coach"
                            loading={false}
                        />
                    </div>

                    <div id="avaialability">
                        <GeneralAvailability />
                    </div>

                    <div id='accomplisments'>
                        <CoachCertifications />
                    </div>

                    <CoachResume />

                    <div id='contact'>
                        <CoachContactInfo />
                    </div>

                    <div id='approach'>
                        <CoachVerification />
                    </div>
                    <CoachDeclaration />

                    <ScrollToTop />
                </div>

                
            </div>
        </>
    )
}
export default CoachProfile
