import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

// Interface for connected user from API
export interface ConnectedUser {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  galleries: any[];
}

// Messages slice state interface
interface MessagesState {
  loading: boolean;
  error: string;
  connectedUsers: ConnectedUser[];
  selectedUserId: number | null;
  apiStatus: string;
}

// Initial state
const initialState: MessagesState = {
  loading: false,
  error: "",
  connectedUsers: [],
  selectedUserId: null,
  apiStatus:""
};



export const fetchMessagesData = createAsyncThunk(
  "messages/fetchMessages",
  async (_, { fulfillWithValue, rejectWithValue }) => {
        debugger;

    const userId = localStorage.getItem("userId")  || "";
    const token = localStorage.getItem("token") || "";
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_NOTIFICATION_API_URL}/messages/connecteduser/${userId}`,
        {
          headers: {
            authorization: `${token}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
        }
      );

      console.log(response.data.data, "response.data.data");

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

// Messages slice
const messagesSlice = createSlice({
  name: "messages",
  initialState,
  reducers: {
    // Action to set selected user
    setSelectedUser: (state, action) => {
      state.selectedUserId = action.payload;
    },
    // Action to clear connected users
    clearConnectedUsers: (state) => {
      state.connectedUsers = [];
      state.selectedUserId = null;
      state.error = "";
    },
    // Action to clear error
    clearError: (state) => {
      state.error = "";
    },
  },
  extraReducers: (builder) => {
    builder
     .addCase(fetchMessagesData.pending, (state) => {
        state.apiStatus = "plansLoading";
      })
      .addCase(fetchMessagesData.fulfilled, (state, action) => {
        state.apiStatus = "plansSuccess";
       state.connectedUsers = action.payload;
      })
      .addCase(fetchMessagesData.rejected, (state, action) => {
        state.apiStatus = "plansFailed";
        state.error = action.payload as string;
      })
  
  },
});

// Export actions
export const { setSelectedUser, clearConnectedUsers, clearError } = messagesSlice.actions;

// Export reducer
export default messagesSlice.reducer;
